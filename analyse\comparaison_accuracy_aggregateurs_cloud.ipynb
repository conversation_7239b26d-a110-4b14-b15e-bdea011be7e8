# 1. Importer les bibliothèques nécessaires
import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Configuration de l'affichage des graphiques
sns.set_theme(style="whitegrid")
plt.rcParams['figure.figsize'] = (14, 8)
plt.rcParams['font.family'] = 'DejaVu Sans'

# 2. Charger et préparer les résultats finaux
RESULTS_DIR = r"../results"

def parse_filename(filename):
    import re
    pattern = re.compile(
        r'^(?P<dataset>\w+)_'
        r'(?P<model>\w+)_'
        r'(?P<fog_aggregator>\w+)_'
        r'(?P<cloud_aggregator>\w+)_'
        r'attack-(?P<attack_type>\w+)'
        r'(?:_(?P<repeat_num>\d+))?'
        r'(?P<is_final>_final)?'
        r'\.(?P<extension>csv|json)$'
    )
    match = pattern.match(filename)
    return match.groupdict() if match else None

def load_final_results(results_dir):
    all_results = []
    for filename in sorted(os.listdir(results_dir)):
        if not filename.endswith('_final.json'):
            continue
        params = parse_filename(filename)
        if not params:
            continue
        with open(os.path.join(results_dir, filename), 'r') as f:
            data = json.load(f)
        config = data.get('config', {})
        final_metrics = data.get('final_metrics', {})
        record = {
            'dataset': config.get('dataset', params.get('dataset')),
            'model': config.get('model', params.get('model')),
            'fog_aggregator': config.get('fog_aggregator', params.get('fog_aggregator')),
            'cloud_aggregator': config.get('cloud_aggregator', params.get('cloud_aggregator')),
            'attack_type': config.get('attack_type', params.get('attack_type')),
            'repeat_num': int(params.get('repeat_num', 0) or 0),
            'malicious_fraction': config.get('malicious_fraction'),
            **final_metrics
        }
        all_results.append(record)
    df = pd.DataFrame(all_results)
    numeric_cols = ['test_accuracy', 'f1_score', 'test_loss', 'malicious_fraction']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    return df

df_results = load_final_results(RESULTS_DIR)


# 3. Afficher un aperçu des données agrégées
if df_results.empty:
    print("Aucune donnée chargée. Vérifiez le dossier de résultats.")
else:
    display(df_results.head())
    print("\nRésumé des agrégateurs cloud disponibles :")
    print(df_results['cloud_aggregator'].value_counts())
    print("\nRésumé des accuracy par agrégateur cloud :")
    print(df_results.groupby('cloud_aggregator')['test_accuracy'].describe())

# 4. Tracer la comparaison de l'accuracy pour chaque agrégateur cloud
if not df_results.empty:
    plt.figure()
    sns.barplot(data=df_results, x='cloud_aggregator', y='test_accuracy', ci='sd')
    plt.title("Comparaison de l'accuracy par agrégateur cloud", fontsize=16)
    plt.xlabel("Agrégateur Cloud")
    plt.ylabel("Accuracy moyenne")
    plt.ylim(0, 1.05)
    plt.tight_layout()
    plt.show()
else:
    print("Aucune donnée à tracer.")

# 5. Enregistrer les graphiques générés
graph_save_path = "comparaison_accuracy_aggregateurs_cloud.png"
if not df_results.empty:
    plt.figure()
    sns.barplot(data=df_results, x='cloud_aggregator', y='test_accuracy', ci='sd')
    plt.title("Comparaison de l'accuracy par agrégateur cloud", fontsize=16)
    plt.xlabel("Agrégateur Cloud")
    plt.ylabel("Accuracy moyenne")
    plt.ylim(0, 1.05)
    plt.tight_layout()
    plt.savefig(graph_save_path)
    print(f"Graphique enregistré sous : {graph_save_path}")
    plt.close()
else:
    print("Aucune donnée à enregistrer.")



# 6. Tracer les courbes d'apprentissage (accuracy vs. round) pour chaque agrégateur cloud
import glob

csv_files = glob.glob(os.path.join(RESULTS_DIR, '*.csv'))

# On suppose que les fichiers CSV suivent le pattern <DATASET>_<MODEL>_<FOG_AGG>_<CLOUD_AGG>_attack-<ATTACK_TYPE>[...].csv
for cloud_agg in df_results['cloud_aggregator'].unique():
    # Chercher les fichiers CSV correspondant à cet agrégateur cloud
    matching_files = [f for f in csv_files if f"_{cloud_agg}_" in os.path.basename(f)]
    if not matching_files:
        print(f"Aucun fichier CSV trouvé pour l'agrégateur cloud : {cloud_agg}")
        continue
    plt.figure(figsize=(10,6))
    for csv_path in matching_files:
        try:
            df_curve = pd.read_csv(csv_path)
            label = os.path.basename(csv_path).replace('.csv','')
            if 'round' in df_curve.columns and 'test_accuracy' in df_curve.columns:
                plt.plot(df_curve['round'], df_curve['test_accuracy'], label=label)
        except Exception as e:
            print(f"Erreur lors de la lecture de {csv_path} : {e}")
    plt.title(f"Courbe d'apprentissage - Accuracy vs. Round\nAgrégateur cloud : {cloud_agg}")
    plt.xlabel('Round')
    plt.ylabel('Test Accuracy')
    plt.ylim(0, 1.05)
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()

# 7. Exemple de courbe style "bande de confiance" appliqué à vos résultats réels

# On suppose que df_results contient les colonnes 'cloud_aggregator', 'test_accuracy', 'repeat_num' et 'round' (si round existe dans les CSV)
import warnings
warnings.filterwarnings('ignore')

# Charger tous les CSV pour obtenir les courbes par round pour chaque cloud_aggregator
import glob
all_curves = []
csv_files = glob.glob(os.path.join(RESULTS_DIR, '*.csv'))
for csv_path in csv_files:
    try:
        df_curve = pd.read_csv(csv_path)
        params = parse_filename(os.path.basename(csv_path))
        if params and 'cloud_aggregator' in params and 'attack_type' in params:
            cloud_agg = params['cloud_aggregator']
            attack = params['attack_type']
            if 'round' in df_curve.columns and 'test_accuracy' in df_curve.columns:
                df_curve = df_curve[['round', 'test_accuracy']].copy()
                df_curve['cloud_aggregator'] = cloud_agg
                df_curve['attack_type'] = attack
                all_curves.append(df_curve)
    except Exception as e:
        print(f"Erreur lecture {csv_path}: {e}")

if all_curves:
    df_all = pd.concat(all_curves, ignore_index=True)
    # Exemple : ne garder qu'une attaque (LabelFlip) et une fraction (si dispo)
    if 'attack_type' in df_all.columns:
        df_all = df_all[df_all['attack_type'] == 'LabelFlip']
    # Calculer moyenne et écart-type par round et agrégateur
    summary = df_all.groupby(['cloud_aggregator', 'round']).agg(
        mean_acc = ('test_accuracy', 'mean'),
        std_acc = ('test_accuracy', 'std')
    ).reset_index()
    plt.figure(figsize=(10,6))
    palette = sns.color_palette('deep', n_colors=summary['cloud_aggregator'].nunique())
    for i, (agg, group) in enumerate(summary.groupby('cloud_aggregator')):
        plt.plot(group['round'], group['mean_acc'], marker='o', label=agg, color=palette[i])
        plt.fill_between(group['round'],
                         group['mean_acc'] - (group['std_acc'].fillna(0)),
                         group['mean_acc'] + (group['std_acc'].fillna(0)),
                         alpha=0.2, color=palette[i])
    plt.title("Accuracy Comparison between Cloud Aggregators in Label Flip Attack (0.3)")
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.grid(True)
    plt.ylim(0, 1.0)
    plt.legend(title='Cloud Aggregator')
    plt.tight_layout()
    plt.show()
else:
    print("Aucune courbe d'apprentissage trouvée dans les CSV.")