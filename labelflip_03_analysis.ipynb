# استيراد المكتبات
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# إعداد الرسومات لتكون أوضح
sns.set(style="whitegrid")
plt.rcParams['figure.figsize'] = (10, 6)



    with open(os.path.join(results_dir, filename), "w") as f:
        f.write(content)
# --- End of Simulation ---

csv_files = glob.glob(os.path.join(results_dir, '*.csv'))

# Group latest results by aggregator based on modification time
# تجميع أحدث نتيجة لكل مجمع حسب تاريخ التعديل
latest = {}
for file in csv_files:
    try:
        # --- FIX: Changed from [2] to [3] to get the cloud aggregator ---
        # --- إصلاح: تم التغيير من [2] إلى [3] للحصول على المجمع السحابي ---
        agg = os.path.basename(file).split('_')[3]
        
        mtime = os.path.getmtime(file)
        
        if agg not in latest or mtime > latest[agg]['mtime']:
            df = pd.read_csv(file)
            if not df.empty:
                last_row = df.iloc[-1]
                latest[agg] = {
                    'mtime': mtime,
                    'round': last_row.get('round', None),
                    'accuracy': last_row.get('accuracy', None),
                    'malicious_fraction': last_row.get('malicious_fraction', None),
                    'file': os.path.basename(file)
                }
    except IndexError:
   

import os
import glob
import pandas as pd
from IPython.display import display

# مسار مجلد النتائج
results_dir = r"ubuntu/hfl_tkinter_project/results"

# البحث عن كل ملفات CSV
csv_files = glob.glob(os.path.join(results_dir, '*.csv'))

# تجميع أحدث نتيجة لكل مجمع حسب تاريخ التعديل
latest = {}
for file in csv_files:
    try:
        # استخراج اسم المجمع السحابي (العنصر الرابع في الاسم)
        agg = os.path.basename(file).split('_')[3]
        
        mtime = os.path.getmtime(file)
        
        # التحقق إذا كان هذا الملف هو الأحدث لهذا المجمع
        if agg not in latest or mtime > latest[agg]['mtime']:
            df = pd.read_csv(file)
            if not df.empty:
                last_row = df.iloc[-1]
                latest[agg] = {
                    'mtime': mtime,
                    'round': last_row.get('round', None),
                    'accuracy': last_row.get('accuracy', None),
                    'malicious_fraction': last_row.get('malicious_fraction', None),
                    'file': os.path.basename(file)
                }
    except IndexError:
        # تجاهل الملفات التي لا تتبع النمط المتوقع في اسمها
        print(f"Skipping file with unexpected name format: {os.path.basename(file)}")

# تحويل النتائج إلى DataFrame للعرض
if latest:
    latest_df = pd.DataFrame([{'cloud_aggregator': k, **v} for k, v in latest.items()])
    
    # اختيار وعرض الأعمدة المطلوبة
    display(latest_df[['cloud_aggregator', 'round', 'accuracy', 'malicious_fraction', 'file']])
else:
    print("No valid CSV files found or processed.")

import os
import glob
import json

# مجلد النتائج النهائية
results_dir = r"ubuntu/hfl_tkinter_project/results"
json_files = glob.glob(os.path.join(results_dir, '*final.json'))

# استخراج الملفات التي تحتوي على malicious_fraction = 0.3
agg_files = {}
for file in json_files:
    with open(file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        frac = data.get('malicious_fraction')
        if frac == 0.3:
            agg = os.path.basename(file).split('_')[2]
            agg_files[agg] = file

if len(agg_files) < 2:
    print('لا يوجد إلا مجمع واحد أو لا توجد نتائج كافية للمقارنة:', list(agg_files.keys()))
else:
    print('تم العثور على النتائج التالية للمقارنة:')
    for agg, path in agg_files.items():
        print(f'{agg}: {path}')


import os
import glob
import json
import matplotlib.pyplot as plt
import pandas as pd

# مجلد النتائج النهائية
results_dir = r"ubuntu/hfl_tkinter_project/results"
json_files = glob.glob(os.path.join(results_dir, '*final.json'))

# تجميع النتائج حيث malicious_fraction = 0.3
agg_names = []
final_accs = []

for file in json_files:
    with open(file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        frac = data.get('malicious_fraction')
        if frac is None:
            frac = data.get('config', {}).get('malicious_fraction')
        if frac == 0.3:
            # استخراج اسم المجمع من config أو من اسم الملف
            agg = data.get('config', {}).get('cloud_aggregator')
            if not agg:
                agg = os.path.basename(file).split('_')[2]
            # استخراج الدقة النهائية من final_metrics
            acc = data.get('final_accuracy')
            if acc is None:
                acc = data.get('final_metrics', {}).get('accuracy')
            if acc is not None:
                agg_names.append(agg)
                final_accs.append(acc)

if len(agg_names) < 2:
    print('لا يوجد إلا مجمع واحد أو لا توجد نتائج كافية للمقارنة:', agg_names)
else:
    # رسم النتائج
    plt.figure(figsize=(8,5))
    plt.bar(agg_names, final_accs)
    plt.xlabel('المجمع')
    plt.ylabel('الدقة النهائية')
    plt.title('مقارنة الدقة النهائية لكل مجمع حيث malicious_fraction = 0.3')
    plt.show()
    # عرض جدول النتائج أيضًا
    df = pd.DataFrame({'aggregator': agg_names, 'accuracy': final_accs})
    display(df)


# استخراج آخر نتيجة (حسب أحدث جولة) لكل cloud_aggregator
if 'cloud_aggregator' in all_data.columns and 'accuracy' in all_data.columns and 'round' in all_data.columns:
    last_results = all_data.sort_values('round').groupby('cloud_aggregator').tail(1)
    display(last_results[['cloud_aggregator', 'round', 'accuracy', 'malicious_fraction']])
else:
    print('Make sure the columns: cloud_aggregator, accuracy, round exist in the data.')


import glob
import os

# تحديد مسار المجلد
results_dir = r"C:\Users\<USER>\Desktop\home\ubuntu\hfl_tkinter_project\results"

# جلب جميع ملفات labelflip بنسبة 0.3
csv_files = glob.glob(os.path.join(results_dir, '*LabelFlip*.csv'))

# عرض الملفات المتوفرة
csv_files

# قراءة جميع البيانات من ملفات labelflip
frames = []
for file in csv_files:
    df = pd.read_csv(file)
    # إذا كان هناك عمود لنسبة الهجوم، صفِّ البيانات
    if 'labelflip' in df.columns:
        filtered = df[df['labelflip'] == 0.3]
        frames.append(filtered)
    else:
        frames.append(df)

# دمج جميع البيانات المصفاة
all_data = pd.concat(frames, ignore_index=True)

# عرض أول 5 صفوف
all_data.head()

# عرض أسماء الأعمدة في البيانات المقروءة
all_data.columns.tolist()

# Compare accuracy between different cloud aggregators
if 'cloud_aggregator' in all_data.columns and 'accuracy' in all_data.columns and 'round' in all_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=all_data, x='round', y='accuracy', hue='cloud_aggregator', marker='o')
    plt.title('Accuracy Comparison between Cloud Aggregators in Label Flip Attack (0.3)')
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Make sure the columns: cloud_aggregator, accuracy, round exist in the data.')

# Compare loss between different cloud aggregators
if 'cloud_aggregator' in all_data.columns and 'loss' in all_data.columns and 'round' in all_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=all_data, x='round', y='loss', hue='cloud_aggregator', marker='o')
    plt.title('Loss Comparison between Cloud Aggregators in Label Flip Attack (0.3)')
    plt.xlabel('Round')
    plt.ylabel('Loss')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Make sure the columns: cloud_aggregator, loss, round exist in the data.')

# Compare F1 score between different cloud aggregators
if 'cloud_aggregator' in all_data.columns and 'f1_score' in all_data.columns and 'round' in all_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=all_data, x='round', y='f1_score', hue='cloud_aggregator', marker='o')
    plt.title('F1 Score Comparison between Cloud Aggregators in Label Flip Attack (0.3)')
    plt.xlabel('Round')
    plt.ylabel('F1 Score')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Make sure the columns: cloud_aggregator, f1_score, round exist in the data.')

# Filter data for malicious_fraction = 0.7 only
filtered_data = all_data[all_data['malicious_fraction'] == 0.7]

# Compare accuracy between cloud aggregators at malicious_fraction = 0.7
if 'cloud_aggregator' in filtered_data.columns and 'accuracy' in filtered_data.columns and 'round' in filtered_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=filtered_data, x='round', y='accuracy', hue='cloud_aggregator', marker='o')
    plt.title('Accuracy Comparison between Cloud Aggregators at malicious_fraction = 0.7')
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Make sure the columns: cloud_aggregator, accuracy, round exist in the data.')

# Compare loss between cloud aggregators at malicious_fraction = 0.7
if 'cloud_aggregator' in filtered_data.columns and 'loss' in filtered_data.columns and 'round' in filtered_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=filtered_data, x='round', y='loss', hue='cloud_aggregator', marker='o')
    plt.title('Loss Comparison between Cloud Aggregators at malicious_fraction = 0.7')
    plt.xlabel('Round')
    plt.ylabel('Loss')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Make sure the columns: cloud_aggregator, loss, round exist in the data.')

# Compare F1 score between cloud aggregators at malicious_fraction = 0.7
if 'cloud_aggregator' in filtered_data.columns and 'f1_score' in filtered_data.columns and 'round' in filtered_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=filtered_data, x='round', y='f1_score', hue='cloud_aggregator', marker='o')
    plt.title('F1 Score Comparison between Cloud Aggregators at malicious_fraction = 0.7')
    plt.xlabel('Round')
    plt.ylabel('F1 Score')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Make sure the columns: cloud_aggregator, f1_score, round exist in the data.')

# Filter data for malicious_fraction = 0.7 (70% label flip attack)
filtered_data = all_data[all_data['malicious_fraction'] == 0.7]

# Compare accuracy between cloud aggregators at 0.7 label flip attack
if 'cloud_aggregator' in filtered_data.columns and 'accuracy' in filtered_data.columns and 'round' in filtered_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=filtered_data, x='round', y='accuracy', hue='cloud_aggregator', marker='o')
    plt.title('Accuracy Comparison between Cloud Aggregators at 0.7 Label Flip Attack')
    plt.xlabel('Round')
    plt.ylabel('Accuracy')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Please make sure the columns: cloud_aggregator, accuracy, round exist in the data.')

# Compare loss between cloud aggregators at 0.7 label flip attack
if 'cloud_aggregator' in filtered_data.columns and 'loss' in filtered_data.columns and 'round' in filtered_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=filtered_data, x='round', y='loss', hue='cloud_aggregator', marker='o')
    plt.title('Loss Comparison between Cloud Aggregators at 0.7 Label Flip Attack')
    plt.xlabel('Round')
    plt.ylabel('Loss')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Please make sure the columns: cloud_aggregator, loss, round exist in the data.')

# Compare F1 score between cloud aggregators at 0.7 label flip attack
if 'cloud_aggregator' in filtered_data.columns and 'f1_score' in filtered_data.columns and 'round' in filtered_data.columns:
    plt.figure(figsize=(12, 6))
    sns.lineplot(data=filtered_data, x='round', y='f1_score', hue='cloud_aggregator', marker='o')
    plt.title('F1 Score Comparison between Cloud Aggregators at 0.7 Label Flip Attack')
    plt.xlabel('Round')
    plt.ylabel('F1 Score')
    plt.legend(title='Cloud Aggregator')
    plt.show()
else:
    print('Please make sure the columns: cloud_aggregator, f1_score, round exist in the data.')