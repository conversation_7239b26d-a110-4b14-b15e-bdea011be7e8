# Main entry point for the HFL Tkinter Application

import tkinter as tk
from src.gui.main_window import MainWindow
import os

if __name__ == "__main__":
    # Create data directory if it doesn't exist
    data_dir = os.path.join(os.path.dirname(__file__), 'src', 'data', 'raw')
    results_dir = os.path.join(os.path.dirname(__file__), 'results')
    os.makedirs(data_dir, exist_ok=True)
    os.makedirs(results_dir, exist_ok=True)
    
    root = tk.Tk()
    app = MainWindow(root)
    root.mainloop()

